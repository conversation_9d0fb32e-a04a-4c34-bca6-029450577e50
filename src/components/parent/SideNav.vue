<template>
  <div class="side-nav" :class="{ 'collapsed': isCollapsed, 'dark': isDarkMode }">
    <div class="logo-container">
      <img src="../../assets/logo.svg" alt="Logo" class="logo-img" />
      <span class="logo-text" v-if="!isCollapsed">WonderFlow</span>
    </div>

    <div class="nav-items" :class="{ 'collapsed': isCollapsed }">
      <router-link to="/home" class="nav-item" active-class="active">
        <el-icon>
          <House />
        </el-icon>
        <span v-if="!isCollapsed">首页</span>
      </router-link>

      <router-link to="/featured-works" class="nav-item" active-class="active">
        <el-icon>
          <Compass />
        </el-icon>
        <span v-if="!isCollapsed">精选创作</span>
      </router-link>

      <router-link to="/video-works" class="nav-item" active-class="active">
        <el-icon>
          <VideoCamera />
        </el-icon>
        <span v-if="!isCollapsed">视频精选</span>
      </router-link>

      <router-link to="/profile" class="nav-item" active-class="active">
        <el-icon>
          <User />
        </el-icon>
        <span v-if="!isCollapsed">个人主页</span>
      </router-link>
      <div class="divider"></div>

      <router-link to="/inputSection" class="nav-item" active-class="active">
        <el-icon>
          <EditPen />
        </el-icon>
        <span v-if="!isCollapsed">创作工坊</span>
      </router-link>

      <router-link to="/stories" class="nav-item" active-class="active" v-if="false">
        <el-icon>
          <Pointer />
        </el-icon>
        <span v-if="!isCollapsed">我的故事</span>
      </router-link>

      <router-link to="/canvas-editor" class="nav-item" active-class="active">
        <el-icon>
          <Crop />
        </el-icon>
        <span v-if="!isCollapsed">视频草稿</span>
      </router-link>

      <div class="divider"></div>

      <router-link to="/image-studio" class="nav-item" active-class="active" v-if="false">
        <el-icon>
          <Picture />
        </el-icon>
        <span v-if="!isCollapsed">创作图片</span>
      </router-link>

      <router-link to="/video-studio" class="nav-item" active-class="active" v-if="false">
        <el-icon>
          <VideoCamera />
        </el-icon>
        <span v-if="!isCollapsed">创作视频</span>
      </router-link>

      <router-link to="/audio-studio" class="nav-item" active-class="active" v-if="false">
        <el-icon>
          <Microphone />
        </el-icon>
        <span v-if="!isCollapsed">创作音频</span>
      </router-link>

      <div class="divider" v-if="false"></div>

      <router-link to="/neo-assets" class="nav-item" active-class="active">
        <el-icon>
          <FolderOpened />
        </el-icon>
        <span v-if="!isCollapsed">媒体库</span>
      </router-link>
    </div>

    <div class="nav-footer">
      <!-- <div class="collapse-btn" @click="toggleCollapse">
        <el-icon v-if="isCollapsed">
          <ArrowRight />
        </el-icon>
        <el-icon v-else>
          <ArrowLeft />
        </el-icon>
      </div> -->

      <div class="footer-content" v-if="!isCollapsed">
        <div class="footer-copyright">
          <div>© WonderFlow.<br />
            <a href="https://beian.miit.gov.cn/#/Integrated/index" rel="noopener noreferrer">
              沪ICP备2025124416
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { House, Compass, User, EditPen, FolderOpened, Pointer, Crop, Picture, VideoCamera, Microphone } from '@element-plus/icons-vue';

const isCollapsed = ref(false);
const isDarkMode = ref(false);

// 定义事件
const emit = defineEmits(['collapse-change']);

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  localStorage.setItem('sideNavCollapsed', isCollapsed.value);
  // 触发事件通知Layout组件
  emit('collapse-change', isCollapsed.value);
  // 同时触发一个自定义DOM事件，以便其他组件也能监听到变化
  window.dispatchEvent(new CustomEvent('sidenav-collapse-change', {
    detail: { collapsed: isCollapsed.value }
  }));
};

// 检查深色模式
const checkDarkMode = () => {
  const savedThemeMode = localStorage.getItem('themeMode');
  if (savedThemeMode === 'dark') {
    isDarkMode.value = true;
  } else if (savedThemeMode === 'auto') {
    isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches;
  } else {
    isDarkMode.value = false;
  }
};

onMounted(() => {
  // 加载保存的折叠状态
  const savedCollapsedState = localStorage.getItem('sideNavCollapsed');
  if (savedCollapsedState !== null) {
    isCollapsed.value = savedCollapsedState === 'true';
    // 初始化时也触发一次事件，确保Layout组件能获取到初始状态
    emit('collapse-change', isCollapsed.value);
  }

  // 检查深色模式
  checkDarkMode();

  // 监听主题变化
  window.addEventListener('theme-changed', checkDarkMode);
});

// 组件卸载前移除事件监听
onUnmounted(() => {
  window.removeEventListener('theme-changed', checkDarkMode);
});

// 导出折叠状态，以便父组件可以直接访问
defineExpose({
  isCollapsed,
  toggleCollapse
});
</script>

<style scoped>
.side-nav {
  width: 200px;
  height: 100vh;
  /* background-color: #f5f7fa; */
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 1000;
}

.side-nav.collapsed {
  width: 60px;
}

body.dark .side-nav {
  /* background-color: var(--bg-primary); */
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.side-nav.dark {
  background-color: var(--bg-primary);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.logo-container {
  display: flex;
  align-items: center;
  padding: 16px;
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.05); */
}

.dark .logo-container {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.logo-img {
  height: 28px;
  width: 28px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  margin-left: 10px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .logo-text {
  color: var(--text-primary);
}

.nav-items {
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  flex: 1;
}

.nav-items.collapsed {
  padding: 0 4px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #606266bf;
  text-decoration: none;
  transition: all 0.3s;
  margin-bottom: 4px;
  border-radius: 10px;
}

.nav-item:hover {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.nav-item.active {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
  font-weight: 500;
}

.dark .nav-item {
  color: #e0e0e0;
}

.dark .nav-item:hover {
  background-color: rgba(64, 158, 255, 0.2);
  color: #409eff;
}

.dark .nav-item.active {
  background-color: rgba(64, 158, 255, 0.2);
  color: #409eff;
}

.nav-item .el-icon {
  font-size: 18px;
  font-weight: 500;
  margin-right: 12px;
  opacity: 0.8;
  vertical-align: middle;
}

.collapsed .nav-item .el-icon {
  margin-right: 0;
}

.nav-item span {
  white-space: nowrap;
  overflow: hidden;
  font-size: 14px;
  font-weight: 600;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 6px 0;
}

.dark .divider {
  background-color: rgba(255, 255, 255, 0.05);
}

.nav-footer {
  padding: 16px;
  display: flex;
  flex-direction: column;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .nav-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.collapse-btn {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
  align-self: flex-end;
  margin-bottom: 10px;
}

.collapse-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.footer-content {
  text-align: center;
  font-size: 12px;
}

.footer-copyright {
  color: #606266;
  font-size: 11px;
  line-height: 1.4;
}

.footer-copyright a {
  color: #606266;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-copyright a:hover {
  color: #409eff;
}

.dark .footer-copyright,
.dark .footer-copyright a {
  color: #a0a0a0;
}

.dark .footer-copyright a:hover {
  color: #409eff;
}

/* @media screen and (max-width: 768px) {
  .side-nav {
    transform: translateX(-100%);
    position: fixed;
  }
  .side-nav.collapsed {
    transform: translateX(0);
    width: 60px;
  }
  .side-nav:not(.collapsed) {
    transform: translateX(0);
  }
} */
</style>